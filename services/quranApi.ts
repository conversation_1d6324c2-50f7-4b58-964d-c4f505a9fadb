import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  ApiResponse,
  Surah,
  Ayah,
  Edition,
  SearchResult,
  QuranApiService,
  CacheItem,
  QuranCache,
} from '@/types/quran';

const BASE_URL = 'http://api.alquran.cloud/v1';
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
const CACHE_KEY = 'quran_cache';

class Quran<PERSON><PERSON> implements QuranApiService {
  private cache: QuranCache = {};

  constructor() {
    this.loadCache();
  }

  // Cache Management
  private async loadCache(): Promise<void> {
    try {
      const cachedData = await AsyncStorage.getItem(CACHE_KEY);
      if (cachedData) {
        this.cache = JSON.parse(cachedData);
      }
    } catch (error) {
      console.warn('Failed to load cache:', error);
    }
  }

  private async saveCache(): Promise<void> {
    try {
      await AsyncStorage.setItem(CACHE_KEY, JSON.stringify(this.cache));
    } catch (error) {
      console.warn('Failed to save cache:', error);
    }
  }

  private isExpired(item: CacheItem<any>): boolean {
    return Date.now() > item.expiresAt;
  }

  private createCacheItem<T>(data: T): CacheItem<T> {
    const now = Date.now();
    return {
      data,
      timestamp: now,
      expiresAt: now + CACHE_DURATION,
    };
  }

  // HTTP Request Helper
  private async fetchApi<T>(endpoint: string): Promise<T> {
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data: ApiResponse<T> = await response.json();
      
      if (data.code !== 200) {
        throw new Error(`API Error ${data.code}: ${data.status}`);
      }

      return data.data;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to fetch ${endpoint}: ${error.message}`);
      }
      throw new Error(`Failed to fetch ${endpoint}: Unknown error`);
    }
  }

  // Public API Methods
  async getSurahs(): Promise<Surah[]> {
    // Check cache first
    if (this.cache.surahs && !this.isExpired(this.cache.surahs)) {
      return this.cache.surahs.data;
    }

    try {
      const surahs = await this.fetchApi<Surah[]>('/surah');
      
      // Cache the result
      this.cache.surahs = this.createCacheItem(surahs);
      await this.saveCache();
      
      return surahs;
    } catch (error) {
      // Return cached data if available, even if expired
      if (this.cache.surahs) {
        console.warn('Using expired cache due to fetch error:', error);
        return this.cache.surahs.data;
      }
      throw error;
    }
  }

  async getSurah(surahNumber: number, edition = 'quran-uthmani'): Promise<Surah> {
    const cacheKey = `${surahNumber}-${edition}`;
    
    // Check cache first
    if (this.cache.surahData?.[cacheKey] && !this.isExpired(this.cache.surahData[cacheKey])) {
      return this.cache.surahData[cacheKey].data;
    }

    try {
      const surah = await this.fetchApi<Surah>(`/surah/${surahNumber}/${edition}`);
      
      // Cache the result
      if (!this.cache.surahData) {
        this.cache.surahData = {};
      }
      this.cache.surahData[cacheKey] = this.createCacheItem(surah);
      await this.saveCache();
      
      return surah;
    } catch (error) {
      // Return cached data if available, even if expired
      if (this.cache.surahData?.[cacheKey]) {
        console.warn('Using expired cache due to fetch error:', error);
        return this.cache.surahData[cacheKey].data;
      }
      throw error;
    }
  }

  async getSurahWithMultipleEditions(surahNumber: number, editions: string[]): Promise<Surah[]> {
    const editionsParam = editions.join(',');
    const endpoint = `/surah/${surahNumber}/editions/${editionsParam}`;
    
    try {
      return await this.fetchApi<Surah[]>(endpoint);
    } catch (error) {
      throw error;
    }
  }

  async getAyah(ayahNumber: number, edition = 'quran-uthmani'): Promise<Ayah> {
    try {
      return await this.fetchApi<Ayah>(`/ayah/${ayahNumber}/${edition}`);
    } catch (error) {
      throw error;
    }
  }

  async getAyahWithMultipleEditions(ayahNumber: number, editions: string[]): Promise<Ayah[]> {
    const editionsParam = editions.join(',');
    const endpoint = `/ayah/${ayahNumber}/editions/${editionsParam}`;
    
    try {
      return await this.fetchApi<Ayah[]>(endpoint);
    } catch (error) {
      throw error;
    }
  }

  async getEditions(format?: 'text' | 'audio', language?: string, type?: string): Promise<Edition[]> {
    const cacheKey = `editions-${format || 'all'}-${language || 'all'}-${type || 'all'}`;
    
    // Check cache first
    if (this.cache.editions && !this.isExpired(this.cache.editions)) {
      let editions = this.cache.editions.data;
      
      // Apply filters
      if (format) editions = editions.filter(e => e.format === format);
      if (language) editions = editions.filter(e => e.language === language);
      if (type) editions = editions.filter(e => e.type === type);
      
      return editions;
    }

    try {
      let endpoint = '/edition';
      const params = new URLSearchParams();
      
      if (format) params.append('format', format);
      if (language) params.append('language', language);
      if (type) params.append('type', type);
      
      if (params.toString()) {
        endpoint += `?${params.toString()}`;
      }

      const editions = await this.fetchApi<Edition[]>(endpoint);
      
      // Cache all editions (without filters) for future use
      if (!format && !language && !type) {
        this.cache.editions = this.createCacheItem(editions);
        await this.saveCache();
      }
      
      return editions;
    } catch (error) {
      throw error;
    }
  }

  async searchVerses(query: string, surah: number | 'all' = 'all', edition = 'en'): Promise<SearchResult> {
    const cacheKey = `search-${query}-${surah}-${edition}`;
    
    // Check cache first
    if (this.cache.searchResults?.[cacheKey] && !this.isExpired(this.cache.searchResults[cacheKey])) {
      return this.cache.searchResults[cacheKey].data;
    }

    try {
      const endpoint = `/search/${encodeURIComponent(query)}/${surah}/${edition}`;
      const result = await this.fetchApi<SearchResult>(endpoint);
      
      // Cache the result
      if (!this.cache.searchResults) {
        this.cache.searchResults = {};
      }
      this.cache.searchResults[cacheKey] = this.createCacheItem(result);
      await this.saveCache();
      
      return result;
    } catch (error) {
      // Return cached data if available, even if expired
      if (this.cache.searchResults?.[cacheKey]) {
        console.warn('Using expired cache due to fetch error:', error);
        return this.cache.searchResults[cacheKey].data;
      }
      throw error;
    }
  }

  getAudioUrl(surahNumber: number, ayahNumber: number, reciterId: string): string {
    // Al-Quran Cloud provides audio URLs in a predictable format
    const paddedSurah = surahNumber.toString().padStart(3, '0');
    const paddedAyah = ayahNumber.toString().padStart(3, '0');
    
    // Different reciters have different URL patterns
    const baseAudioUrl = 'https://cdn.islamic.network/quran/audio';
    
    switch (reciterId) {
      case 'ar.alafasy':
        return `${baseAudioUrl}/128/ar.alafasy/${paddedSurah}${paddedAyah}.mp3`;
      case 'ar.husary':
        return `${baseAudioUrl}/128/ar.husary/${paddedSurah}${paddedAyah}.mp3`;
      case 'ar.abdulbasitmurattal':
        return `${baseAudioUrl}/128/ar.abdulbasitmurattal/${paddedSurah}${paddedAyah}.mp3`;
      case 'ar.mahermuaiqly':
        return `${baseAudioUrl}/128/ar.mahermuaiqly/${paddedSurah}${paddedAyah}.mp3`;
      default:
        return `${baseAudioUrl}/128/${reciterId}/${paddedSurah}${paddedAyah}.mp3`;
    }
  }

  // Utility Methods
  async clearCache(): Promise<void> {
    this.cache = {};
    await AsyncStorage.removeItem(CACHE_KEY);
  }

  async getCacheSize(): Promise<number> {
    try {
      const cachedData = await AsyncStorage.getItem(CACHE_KEY);
      return cachedData ? new Blob([cachedData]).size : 0;
    } catch {
      return 0;
    }
  }

  // Preload commonly used data
  async preloadEssentialData(): Promise<void> {
    try {
      await Promise.all([
        this.getSurahs(),
        this.getEditions('audio', 'ar'),
        this.getSurah(1), // Al-Fatiha
        this.getSurah(2), // Al-Baqarah (first few verses)
      ]);
    } catch (error) {
      console.warn('Failed to preload essential data:', error);
    }
  }
}

// Export singleton instance
export const quranApi = new QuranApi();
export default quranApi;
