import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import NetInfo from '@react-native-community/netinfo';

interface NetworkStatusProps {
  onConnectionChange?: (isConnected: boolean) => void;
}

export function NetworkStatus({ onConnectionChange }: NetworkStatusProps) {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [showBanner, setShowBanner] = useState(false);
  const slideAnim = new Animated.Value(-50);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      const connected = state.isConnected && state.isInternetReachable;
      
      if (isConnected !== null && isConnected !== connected) {
        setShowBanner(true);
        
        // Slide in animation
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start();

        // Hide banner after 3 seconds
        setTimeout(() => {
          Animated.timing(slideAnim, {
            toValue: -50,
            duration: 300,
            useNativeDriver: true,
          }).start(() => {
            setShowBanner(false);
          });
        }, 3000);
      }
      
      setIsConnected(connected);
      onConnectionChange?.(connected || false);
    });

    return unsubscribe;
  }, [isConnected, onConnectionChange]);

  if (!showBanner || isConnected === null) {
    return null;
  }

  return (
    <Animated.View 
      style={[
        styles.banner,
        {
          backgroundColor: isConnected ? '#4CAF50' : '#FF6B6B',
          transform: [{ translateY: slideAnim }],
        }
      ]}
    >
      <Ionicons 
        name={isConnected ? 'wifi' : 'wifi-outline'} 
        size={16} 
        color="white" 
      />
      <Text style={styles.bannerText}>
        {isConnected ? 'Back online' : 'No internet connection'}
      </Text>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  banner: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingTop: 50, // Account for status bar
    zIndex: 1000,
  },
  bannerText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
});
