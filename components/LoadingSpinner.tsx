import React from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { ThemedView } from './ThemedView';
import { ThemedText } from './ThemedText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

interface LoadingSpinnerProps {
  message?: string;
  size?: 'small' | 'large';
  overlay?: boolean;
  color?: string;
}

export function LoadingSpinner({ 
  message = 'Loading...', 
  size = 'large', 
  overlay = false,
  color 
}: LoadingSpinnerProps) {
  const colorScheme = useColorScheme();
  const spinnerColor = color || Colors[colorScheme ?? 'light'].tint;

  const content = (
    <View style={styles.container}>
      <ActivityIndicator size={size} color={spinnerColor} />
      {message && (
        <ThemedText type="default" style={styles.message}>
          {message}
        </ThemedText>
      )}
    </View>
  );

  if (overlay) {
    return (
      <View style={styles.overlay}>
        <View style={[styles.overlayContent, { backgroundColor: Colors[colorScheme ?? 'light'].background }]}>
          {content}
        </View>
      </View>
    );
  }

  return (
    <ThemedView style={styles.fullScreen}>
      {content}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullScreen: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  message: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  overlayContent: {
    padding: 24,
    borderRadius: 12,
    minWidth: 120,
    alignItems: 'center',
  },
});
