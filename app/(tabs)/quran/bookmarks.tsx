import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useQuran } from '@/contexts/QuranContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Bookmark } from '@/types/quran';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    FlatList,
    Modal,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

export default function BookmarksScreen() {
  const colorScheme = useColorScheme();
  const { bookmarks, removeBookmark, updateBookmark } = useQuran();
  
  const [selectedBookmark, setSelectedBookmark] = useState<Bookmark | null>(null);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [editNote, setEditNote] = useState('');
  const [editTags, setEditTags] = useState('');

  const handleBookmarkPress = (bookmark: Bookmark) => {
    router.push(`/(tabs)/quran/surah/${bookmark.surahNumber}` as any);
  };

  const handleEditBookmark = (bookmark: Bookmark) => {
    setSelectedBookmark(bookmark);
    setEditNote(bookmark.note || '');
    setEditTags(bookmark.tags?.join(', ') || '');
    setIsEditModalVisible(true);
  };

  const handleDeleteBookmark = (bookmark: Bookmark) => {
    Alert.alert(
      'Delete Bookmark',
      'Are you sure you want to delete this bookmark?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => removeBookmark(bookmark.id),
        },
      ]
    );
  };

  const handleSaveEdit = async () => {
    if (!selectedBookmark) return;

    try {
      const tags = editTags
        .split(',')
        .map(tag => tag.trim())
        .filter(tag => tag.length > 0);

      await updateBookmark(selectedBookmark.id, {
        note: editNote.trim() || undefined,
        tags: tags.length > 0 ? tags : undefined,
      });

      setIsEditModalVisible(false);
      setSelectedBookmark(null);
      setEditNote('');
      setEditTags('');
    } catch (error) {
      Alert.alert('Error', 'Failed to update bookmark');
    }
  };

  const renderBookmarkItem = ({ item }: { item: Bookmark }) => (
    <TouchableOpacity
      style={[
        styles.bookmarkItem,
        { 
          backgroundColor: Colors[colorScheme ?? 'light'].background,
          borderBottomColor: Colors[colorScheme ?? 'light'].tabIconDefault,
        }
      ]}
      onPress={() => handleBookmarkPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.bookmarkContent}>
        {/* Header */}
        <View style={styles.bookmarkHeader}>
          <View style={styles.surahInfo}>
            <ThemedText type="defaultSemiBold" style={styles.surahName}>
              {item.surahName}
            </ThemedText>
            <ThemedText type="default" style={styles.ayahNumber}>
              Verse {item.ayahNumber}
            </ThemedText>
          </View>
          
          <View style={styles.bookmarkActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleEditBookmark(item)}
            >
              <Ionicons 
                name="create-outline" 
                size={20} 
                color={Colors[colorScheme ?? 'light'].tint} 
              />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDeleteBookmark(item)}
            >
              <Ionicons 
                name="trash-outline" 
                size={20} 
                color="#FF6B6B" 
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Ayah Text */}
        <ThemedText style={styles.ayahText} numberOfLines={3}>
          {item.ayahText}
        </ThemedText>

        {/* Note */}
        {item.note && (
          <View style={styles.noteContainer}>
            <Ionicons 
              name="document-text-outline" 
              size={14} 
              color={Colors[colorScheme ?? 'light'].tabIconDefault} 
            />
            <ThemedText style={styles.noteText} numberOfLines={2}>
              {item.note}
            </ThemedText>
          </View>
        )}

        {/* Tags */}
        {item.tags && item.tags.length > 0 && (
          <View style={styles.tagsContainer}>
            {item.tags.map((tag, index) => (
              <View 
                key={index}
                style={[
                  styles.tag,
                  { backgroundColor: Colors[colorScheme ?? 'light'].tint + '20' }
                ]}
              >
                <Text style={[
                  styles.tagText,
                  { color: Colors[colorScheme ?? 'light'].tint }
                ]}>
                  {tag}
                </Text>
              </View>
            ))}
          </View>
        )}

        {/* Date */}
        <ThemedText type="default" style={styles.dateText}>
          Saved {new Date(item.createdAt).toLocaleDateString()}
        </ThemedText>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons 
        name="bookmark-outline" 
        size={64} 
        color={Colors[colorScheme ?? 'light'].tabIconDefault} 
      />
      <ThemedText type="subtitle" style={styles.emptyTitle}>
        No bookmarks yet
      </ThemedText>
      <ThemedText type="default" style={styles.emptyDescription}>
        Bookmark your favorite verses while reading to save them here
      </ThemedText>
    </View>
  );

  const renderEditModal = () => (
    <Modal
      visible={isEditModalVisible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setIsEditModalVisible(false)}
    >
      <ThemedView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity
            onPress={() => setIsEditModalVisible(false)}
          >
            <ThemedText style={styles.cancelButton}>Cancel</ThemedText>
          </TouchableOpacity>
          
          <ThemedText type="defaultSemiBold" style={styles.modalTitle}>
            Edit Bookmark
          </ThemedText>
          
          <TouchableOpacity onPress={handleSaveEdit}>
            <ThemedText style={[
              styles.saveButton,
              { color: Colors[colorScheme ?? 'light'].tint }
            ]}>
              Save
            </ThemedText>
          </TouchableOpacity>
        </View>

        <View style={styles.modalContent}>
          {selectedBookmark && (
            <>
              <View style={styles.bookmarkPreview}>
                <ThemedText type="defaultSemiBold">
                  {selectedBookmark.surahName} - Verse {selectedBookmark.ayahNumber}
                </ThemedText>
                <ThemedText style={styles.previewText} numberOfLines={2}>
                  {selectedBookmark.ayahText}
                </ThemedText>
              </View>

              <View style={styles.inputGroup}>
                <ThemedText type="defaultSemiBold" style={styles.inputLabel}>
                  Note
                </ThemedText>
                <TextInput
                  style={[
                    styles.textInput,
                    styles.noteInput,
                    { 
                      color: Colors[colorScheme ?? 'light'].text,
                      borderColor: Colors[colorScheme ?? 'light'].tabIconDefault,
                    }
                  ]}
                  placeholder="Add a personal note..."
                  placeholderTextColor={Colors[colorScheme ?? 'light'].tabIconDefault}
                  value={editNote}
                  onChangeText={setEditNote}
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                />
              </View>

              <View style={styles.inputGroup}>
                <ThemedText type="defaultSemiBold" style={styles.inputLabel}>
                  Tags
                </ThemedText>
                <TextInput
                  style={[
                    styles.textInput,
                    { 
                      color: Colors[colorScheme ?? 'light'].text,
                      borderColor: Colors[colorScheme ?? 'light'].tabIconDefault,
                    }
                  ]}
                  placeholder="Enter tags separated by commas..."
                  placeholderTextColor={Colors[colorScheme ?? 'light'].tabIconDefault}
                  value={editTags}
                  onChangeText={setEditTags}
                />
                <ThemedText type="default" style={styles.inputHint}>
                  Example: reflection, favorite, memorize
                </ThemedText>
              </View>
            </>
          )}
        </View>
      </ThemedView>
    </Modal>
  );

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.navigation}>
          <TouchableOpacity
            style={styles.navButton}
            onPress={() => router.back()}
          >
            <Ionicons 
              name="arrow-back" 
              size={24} 
              color={Colors[colorScheme ?? 'light'].text} 
            />
          </TouchableOpacity>
          
          <ThemedText type="title" style={styles.title}>
            Bookmarks
          </ThemedText>
          
          <View style={styles.navButton} />
        </View>

        {bookmarks.length > 0 && (
          <ThemedText type="default" style={styles.bookmarkCount}>
            {bookmarks.length} bookmark{bookmarks.length !== 1 ? 's' : ''}
          </ThemedText>
        )}
      </View>

      {/* Content */}
      <FlatList
        data={bookmarks}
        renderItem={renderBookmarkItem}
        keyExtractor={(item) => item.id}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={bookmarks.length === 0 ? styles.emptyContainer : styles.listContainer}
      />

      {renderEditModal()}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  navigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  navButton: {
    padding: 8,
    width: 40,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  bookmarkCount: {
    textAlign: 'center',
    fontSize: 14,
    opacity: 0.6,
  },
  emptyContainer: {
    flexGrow: 1,
  },
  listContainer: {
    paddingBottom: 20,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    textAlign: 'center',
    opacity: 0.6,
  },
  bookmarkItem: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  bookmarkContent: {
    flex: 1,
  },
  bookmarkHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  surahInfo: {
    flex: 1,
  },
  surahName: {
    fontSize: 16,
    marginBottom: 2,
  },
  ayahNumber: {
    fontSize: 12,
    opacity: 0.6,
  },
  bookmarkActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
  ayahText: {
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 8,
  },
  noteContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
  },
  noteText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 13,
    fontStyle: 'italic',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  tag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 6,
    marginBottom: 4,
  },
  tagText: {
    fontSize: 11,
    fontWeight: '600',
  },
  dateText: {
    fontSize: 11,
    opacity: 0.5,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E0E0E0',
  },
  cancelButton: {
    fontSize: 16,
    color: '#FF6B6B',
  },
  modalTitle: {
    fontSize: 18,
  },
  saveButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  bookmarkPreview: {
    padding: 16,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    marginBottom: 24,
  },
  previewText: {
    marginTop: 8,
    fontSize: 14,
    opacity: 0.7,
  },
  inputGroup: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
  },
  noteInput: {
    height: 80,
  },
  inputHint: {
    marginTop: 4,
    fontSize: 12,
    opacity: 0.6,
  },
});
