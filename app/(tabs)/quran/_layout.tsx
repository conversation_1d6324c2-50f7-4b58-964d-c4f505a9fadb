import { Stack } from 'expo-router';
import React from 'react';
import { QuranProvider } from '@/contexts/QuranContext';

export default function QuranLayout() {
  return (
    <QuranProvider>
      <Stack
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen
          name="index"
          options={{
            title: 'Quran',
          }}
        />
        <Stack.Screen
          name="surah/[id]"
          options={{
            title: 'Surah',
            presentation: 'card',
          }}
        />
        <Stack.Screen
          name="search"
          options={{
            title: 'Search',
            presentation: 'modal',
          }}
        />
        <Stack.Screen
          name="bookmarks"
          options={{
            title: 'Bookmarks',
          }}
        />
        <Stack.Screen
          name="settings"
          options={{
            title: 'Settings',
          }}
        />
      </Stack>
    </QuranProvider>
  );
}
