import { ErrorBoundary } from '@/components/ErrorBoundary';
import { NetworkStatus } from '@/components/NetworkStatus';
import { QuranProvider } from '@/contexts/QuranContext';
import { Stack } from 'expo-router';
import React from 'react';

export default function QuranLayout() {
  return (
    <ErrorBoundary>
      <QuranProvider>
        <NetworkStatus />
        <Stack
          screenOptions={{
            headerShown: false,
          }}
        >
          <Stack.Screen
            name="index"
            options={{
              title: 'Quran',
            }}
          />
          <Stack.Screen
            name="surah/[id]"
            options={{
              title: 'Surah',
              presentation: 'card',
            }}
          />
          <Stack.Screen
            name="search"
            options={{
              title: 'Search',
              presentation: 'modal',
            }}
          />
          <Stack.Screen
            name="bookmarks"
            options={{
              title: 'Bookmarks',
            }}
          />
          <Stack.Screen
            name="settings"
            options={{
              title: 'Settings',
            }}
          />
        </Stack>
      </QuranProvider>
    </ErrorBoundary>
  );
}
