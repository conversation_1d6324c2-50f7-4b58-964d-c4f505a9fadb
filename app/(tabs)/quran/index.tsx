import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useQuran } from '@/contexts/QuranContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useMemo, useState } from 'react';
import {
    ActivityIndicator,
    FlatList,
    RefreshControl,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

export default function SurahListScreen() {
  const colorScheme = useColorScheme();
  const { surahs, isLoading, error, loadSurahs, bookmarks, readingProgress } = useQuran();
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  // Filter surahs based on search query
  const filteredSurahs = useMemo(() => {
    if (!searchQuery.trim()) return surahs;
    
    const query = searchQuery.toLowerCase();
    return surahs.filter(surah => 
      surah.englishName.toLowerCase().includes(query) ||
      surah.englishNameTranslation.toLowerCase().includes(query) ||
      surah.number.toString().includes(query)
    );
  }, [surahs, searchQuery]);

  // Enhanced surah data with bookmark and progress info
  const enhancedSurahs = useMemo(() => {
    return filteredSurahs.map(surah => {
      const isBookmarked = bookmarks.some(bookmark => bookmark.surahNumber === surah.number);
      const progress = readingProgress.find(p => p.surahNumber === surah.number);
      
      return {
        ...surah,
        isBookmarked,
        lastRead: progress?.timestamp,
        progress: progress?.percentage || 0,
      };
    });
  }, [filteredSurahs, bookmarks, readingProgress]);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await loadSurahs();
    } catch (error) {
      console.error('Failed to refresh surahs:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleSurahPress = (surahNumber: number) => {
    router.push(`/(tabs)/quran/surah/${surahNumber}` as any);
  };

  const renderSurahItem = ({ item }: { item: typeof enhancedSurahs[0] }) => (
    <TouchableOpacity
      style={[
        styles.surahItem,
        { 
          backgroundColor: Colors[colorScheme ?? 'light'].background,
          borderBottomColor: Colors[colorScheme ?? 'light'].tabIconDefault,
        }
      ]}
      onPress={() => handleSurahPress(item.number)}
      activeOpacity={0.7}
    >
      <View style={styles.surahContent}>
        {/* Surah Number Circle */}
        <View style={[
          styles.surahNumber,
          { backgroundColor: Colors[colorScheme ?? 'light'].tint }
        ]}>
          <Text style={[styles.surahNumberText, { color: 'white' }]}>
            {item.number}
          </Text>
        </View>

        {/* Surah Info */}
        <View style={styles.surahInfo}>
          <View style={styles.surahHeader}>
            <ThemedText type="subtitle" style={styles.englishName}>
              {item.englishName}
            </ThemedText>
            {item.isBookmarked && (
              <Ionicons 
                name="bookmark" 
                size={16} 
                color={Colors[colorScheme ?? 'light'].tint} 
              />
            )}
          </View>
          
          <ThemedText type="default" style={styles.translation}>
            {item.englishNameTranslation}
          </ThemedText>
          
          <View style={styles.surahMeta}>
            <ThemedText type="defaultSemiBold" style={styles.metaText}>
              {item.numberOfAyahs} verses
            </ThemedText>
            <Text style={styles.separator}>•</Text>
            <ThemedText 
              type="default" 
              style={[
                styles.metaText,
                { color: item.revelationType === 'Meccan' ? '#8B5A2B' : '#2B5A8B' }
              ]}
            >
              {item.revelationType}
            </ThemedText>
          </View>

          {/* Progress Bar */}
          {item.progress > 0 && (
            <View style={styles.progressContainer}>
              <View style={[
                styles.progressBar,
                { backgroundColor: Colors[colorScheme ?? 'light'].tabIconDefault }
              ]}>
                <View 
                  style={[
                    styles.progressFill,
                    { 
                      width: `${item.progress}%`,
                      backgroundColor: Colors[colorScheme ?? 'light'].tint
                    }
                  ]} 
                />
              </View>
              <ThemedText type="default" style={styles.progressText}>
                {Math.round(item.progress)}%
              </ThemedText>
            </View>
          )}
        </View>

        {/* Arabic Name */}
        <View style={styles.arabicContainer}>
          <Text style={[
            styles.arabicName,
            { color: Colors[colorScheme ?? 'light'].text }
          ]}>
            {item.name}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <ThemedText type="title" style={styles.title}>
        القرآن الكريم
      </ThemedText>
      <ThemedText type="subtitle" style={styles.subtitle}>
        The Holy Quran
      </ThemedText>
      
      {/* Search Bar */}
      <View style={[
        styles.searchContainer,
        { 
          backgroundColor: Colors[colorScheme ?? 'light'].background,
          borderColor: Colors[colorScheme ?? 'light'].tabIconDefault,
        }
      ]}>
        <Ionicons 
          name="search" 
          size={20} 
          color={Colors[colorScheme ?? 'light'].tabIconDefault} 
          style={styles.searchIcon}
        />
        <TextInput
          style={[
            styles.searchInput,
            { color: Colors[colorScheme ?? 'light'].text }
          ]}
          placeholder="Search surahs..."
          placeholderTextColor={Colors[colorScheme ?? 'light'].tabIconDefault}
          value={searchQuery}
          onChangeText={setSearchQuery}
          autoCapitalize="none"
          autoCorrect={false}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons
              name="close-circle"
              size={20}
              color={Colors[colorScheme ?? 'light'].tabIconDefault}
            />
          </TouchableOpacity>
        )}
        <TouchableOpacity
          style={styles.advancedSearchButton}
          onPress={() => router.push('/(tabs)/quran/search' as any)}
        >
          <Ionicons
            name="search-circle"
            size={24}
            color={Colors[colorScheme ?? 'light'].tint}
          />
        </TouchableOpacity>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity
          style={[
            styles.quickActionButton,
            { backgroundColor: Colors[colorScheme ?? 'light'].tint + '20' }
          ]}
          onPress={() => router.push('/(tabs)/quran/bookmarks' as any)}
        >
          <Ionicons
            name="bookmark"
            size={16}
            color={Colors[colorScheme ?? 'light'].tint}
          />
          <Text style={[
            styles.quickActionText,
            { color: Colors[colorScheme ?? 'light'].tint }
          ]}>
            Bookmarks ({bookmarks.length})
          </Text>
        </TouchableOpacity>
      </View>

      {/* Results Count */}
      {searchQuery.trim() && (
        <ThemedText type="default" style={styles.resultsCount}>
          {filteredSurahs.length} of {surahs.length} surahs
        </ThemedText>
      )}
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons 
        name="book-outline" 
        size={64} 
        color={Colors[colorScheme ?? 'light'].tabIconDefault} 
      />
      <ThemedText type="subtitle" style={styles.emptyTitle}>
        {searchQuery ? 'No surahs found' : 'No surahs available'}
      </ThemedText>
      <ThemedText type="default" style={styles.emptyDescription}>
        {searchQuery 
          ? 'Try adjusting your search terms'
          : 'Pull down to refresh and load surahs'
        }
      </ThemedText>
    </View>
  );

  const renderError = () => (
    <View style={styles.errorState}>
      <Ionicons 
        name="alert-circle-outline" 
        size={64} 
        color="#FF6B6B" 
      />
      <ThemedText type="subtitle" style={styles.errorTitle}>
        Failed to load surahs
      </ThemedText>
      <ThemedText type="default" style={styles.errorDescription}>
        {error}
      </ThemedText>
      <TouchableOpacity 
        style={[
          styles.retryButton,
          { backgroundColor: Colors[colorScheme ?? 'light'].tint }
        ]}
        onPress={handleRefresh}
      >
        <Text style={styles.retryButtonText}>Try Again</Text>
      </TouchableOpacity>
    </View>
  );

  if (isLoading && surahs.length === 0) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.loadingState}>
          <ActivityIndicator 
            size="large" 
            color={Colors[colorScheme ?? 'light'].tint} 
          />
          <ThemedText type="default" style={styles.loadingText}>
            Loading surahs...
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (error && surahs.length === 0) {
    return (
      <ThemedView style={styles.container}>
        {renderError()}
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <FlatList
        data={enhancedSurahs}
        renderItem={renderSurahItem}
        keyExtractor={(item) => item.number.toString()}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors[colorScheme ?? 'light'].tint]}
            tintColor={Colors[colorScheme ?? 'light'].tint}
          />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={enhancedSurahs.length === 0 ? styles.emptyContainer : undefined}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    textAlign: 'center',
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    textAlign: 'center',
    fontSize: 16,
    marginBottom: 20,
    opacity: 0.7,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 10,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 4,
  },
  advancedSearchButton: {
    marginLeft: 8,
  },
  quickActions: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  quickActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  quickActionText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  resultsCount: {
    textAlign: 'center',
    fontSize: 14,
    opacity: 0.6,
  },
  surahItem: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  surahContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  surahNumber: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  surahNumberText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  surahInfo: {
    flex: 1,
    marginRight: 16,
  },
  surahHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  englishName: {
    fontSize: 18,
    fontWeight: '600',
  },
  translation: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 6,
  },
  surahMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaText: {
    fontSize: 12,
  },
  separator: {
    marginHorizontal: 8,
    opacity: 0.5,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  progressBar: {
    flex: 1,
    height: 4,
    borderRadius: 2,
    marginRight: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 10,
    minWidth: 30,
  },
  arabicContainer: {
    alignItems: 'flex-end',
  },
  arabicName: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'right',
  },
  loadingState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  emptyContainer: {
    flexGrow: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    textAlign: 'center',
    opacity: 0.6,
  },
  errorState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
    color: '#FF6B6B',
  },
  errorDescription: {
    textAlign: 'center',
    opacity: 0.6,
    marginBottom: 24,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
