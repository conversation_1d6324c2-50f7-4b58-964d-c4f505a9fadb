import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useQuran } from '@/contexts/QuranContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { AudioReciter, Edition } from '@/types/quran';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    FlatList,
    Modal,
    ScrollView,
    StyleSheet,
    Switch,
    TouchableOpacity,
    View
} from 'react-native';

export default function SettingsScreen() {
  const colorScheme = useColorScheme();
  const {
    settings,
    updateSettings,
    editions,
    audioReciters,
    offlineStatus,
    cachePopularSurahs,
    syncCache,
    clearCache,
  } = useQuran();
  
  const [isTranslationModalVisible, setIsTranslationModalVisible] = useState(false);
  const [isReciterModalVisible, setIsReciterModalVisible] = useState(false);
  const [isThemeModalVisible, setIsThemeModalVisible] = useState(false);

  const handleFontSizeChange = (value: number) => {
    updateSettings({ fontSize: Math.round(value) });
  };

  const handleTranslationToggle = (value: boolean) => {
    updateSettings({ showTranslation: value });
  };

  const handleTransliterationToggle = (value: boolean) => {
    updateSettings({ showTransliteration: value });
  };

  const handleAutoPlayToggle = (value: boolean) => {
    updateSettings({ autoPlay: value });
  };

  const handleTranslationSelect = (edition: Edition) => {
    updateSettings({ translationEdition: edition.identifier });
    setIsTranslationModalVisible(false);
  };

  const handleReciterSelect = (reciter: AudioReciter) => {
    updateSettings({ audioReciter: reciter.identifier });
    setIsReciterModalVisible(false);
  };

  const handleThemeSelect = (theme: 'light' | 'dark' | 'sepia') => {
    updateSettings({ theme });
    setIsThemeModalVisible(false);
  };

  const handleCachePopularSurahs = async () => {
    try {
      await cachePopularSurahs();
      Alert.alert('Success', 'Popular surahs have been cached for offline reading');
    } catch (error) {
      Alert.alert('Error', 'Failed to cache surahs');
    }
  };

  const handleSyncCache = async () => {
    try {
      await syncCache();
      Alert.alert('Success', 'Cache has been synced with latest data');
    } catch (error) {
      Alert.alert('Error', 'Failed to sync cache');
    }
  };

  const handleClearCache = async () => {
    Alert.alert(
      'Clear Cache',
      'This will remove all cached data. You will need an internet connection to read the Quran.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              await clearCache();
              Alert.alert('Success', 'Cache has been cleared');
            } catch (error) {
              Alert.alert('Error', 'Failed to clear cache');
            }
          },
        },
      ]
    );
  };

  const formatCacheSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleRepeatModeChange = () => {
    const modes: Array<'none' | 'verse' | 'surah'> = ['none', 'verse', 'surah'];
    const currentIndex = modes.indexOf(settings.repeatMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    updateSettings({ repeatMode: modes[nextIndex] });
  };

  const renderSettingItem = (
    title: string,
    subtitle?: string,
    rightComponent?: React.ReactNode,
    onPress?: () => void
  ) => (
    <TouchableOpacity
      style={[
        styles.settingItem,
        { borderBottomColor: Colors[colorScheme ?? 'light'].tabIconDefault }
      ]}
      onPress={onPress}
      disabled={!onPress}
      activeOpacity={onPress ? 0.7 : 1}
    >
      <View style={styles.settingContent}>
        <View style={styles.settingText}>
          <ThemedText type="defaultSemiBold" style={styles.settingTitle}>
            {title}
          </ThemedText>
          {subtitle && (
            <ThemedText type="default" style={styles.settingSubtitle}>
              {subtitle}
            </ThemedText>
          )}
        </View>
        {rightComponent}
      </View>
    </TouchableOpacity>
  );

  const renderSelectionModal = (
    visible: boolean,
    onClose: () => void,
    title: string,
    data: any[],
    renderItem: (item: any) => React.ReactNode,
    keyExtractor: (item: any) => string
  ) => (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <ThemedView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={onClose}>
            <ThemedText style={styles.cancelButton}>Cancel</ThemedText>
          </TouchableOpacity>
          
          <ThemedText type="defaultSemiBold" style={styles.modalTitle}>
            {title}
          </ThemedText>
          
          <View style={styles.navButton} />
        </View>

        <FlatList
          data={data}
          renderItem={({ item }) => renderItem(item)}
          keyExtractor={keyExtractor}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.modalList}
        />
      </ThemedView>
    </Modal>
  );

  const currentTranslation = editions.find(e => e.identifier === settings.translationEdition);
  const currentReciter = audioReciters.find(r => r.identifier === settings.audioReciter);

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.navigation}>
          <TouchableOpacity
            style={styles.navButton}
            onPress={() => router.back()}
          >
            <Ionicons 
              name="arrow-back" 
              size={24} 
              color={Colors[colorScheme ?? 'light'].text} 
            />
          </TouchableOpacity>
          
          <ThemedText type="title" style={styles.title}>
            Settings
          </ThemedText>
          
          <View style={styles.navButton} />
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Reading Settings */}
        <View style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Reading
          </ThemedText>

          {renderSettingItem(
            'Font Size',
            `${settings.fontSize}pt`,
            <View style={styles.sliderContainer}>
              <Slider
                style={styles.slider}
                minimumValue={12}
                maximumValue={32}
                value={settings.fontSize}
                onValueChange={handleFontSizeChange}
                minimumTrackTintColor={Colors[colorScheme ?? 'light'].tint}
                maximumTrackTintColor={Colors[colorScheme ?? 'light'].tabIconDefault}
                thumbTintColor={Colors[colorScheme ?? 'light'].tint}
                step={1}
              />
            </View>
          )}

          {renderSettingItem(
            'Show Translation',
            'Display verse translations',
            <Switch
              value={settings.showTranslation}
              onValueChange={handleTranslationToggle}
              trackColor={{ 
                false: Colors[colorScheme ?? 'light'].tabIconDefault, 
                true: Colors[colorScheme ?? 'light'].tint 
              }}
              thumbColor="white"
            />
          )}

          {settings.showTranslation && renderSettingItem(
            'Translation',
            currentTranslation?.englishName || 'Select translation',
            <Ionicons 
              name="chevron-forward" 
              size={20} 
              color={Colors[colorScheme ?? 'light'].tabIconDefault} 
            />,
            () => setIsTranslationModalVisible(true)
          )}

          {renderSettingItem(
            'Show Transliteration',
            'Display phonetic pronunciation',
            <Switch
              value={settings.showTransliteration}
              onValueChange={handleTransliterationToggle}
              trackColor={{ 
                false: Colors[colorScheme ?? 'light'].tabIconDefault, 
                true: Colors[colorScheme ?? 'light'].tint 
              }}
              thumbColor="white"
            />
          )}
        </View>

        {/* Audio Settings */}
        <View style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Audio
          </ThemedText>

          {renderSettingItem(
            'Reciter',
            currentReciter?.englishName || 'Select reciter',
            <Ionicons 
              name="chevron-forward" 
              size={20} 
              color={Colors[colorScheme ?? 'light'].tabIconDefault} 
            />,
            () => setIsReciterModalVisible(true)
          )}

          {renderSettingItem(
            'Auto Play',
            'Automatically play next verse',
            <Switch
              value={settings.autoPlay}
              onValueChange={handleAutoPlayToggle}
              trackColor={{ 
                false: Colors[colorScheme ?? 'light'].tabIconDefault, 
                true: Colors[colorScheme ?? 'light'].tint 
              }}
              thumbColor="white"
            />
          )}

          {renderSettingItem(
            'Repeat Mode',
            settings.repeatMode === 'none' ? 'Off' : 
             settings.repeatMode === 'verse' ? 'Repeat Verse' : 'Repeat Surah',
            <Ionicons 
              name="chevron-forward" 
              size={20} 
              color={Colors[colorScheme ?? 'light'].tabIconDefault} 
            />,
            handleRepeatModeChange
          )}
        </View>

        {/* Appearance */}
        <View style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Appearance
          </ThemedText>

          {renderSettingItem(
            'Theme',
            settings.theme === 'light' ? 'Light' :
             settings.theme === 'dark' ? 'Dark' : 'Sepia',
            <Ionicons
              name="chevron-forward"
              size={20}
              color={Colors[colorScheme ?? 'light'].tabIconDefault}
            />,
            () => setIsThemeModalVisible(true)
          )}
        </View>

        {/* Offline */}
        <View style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            Offline Reading
          </ThemedText>

          {renderSettingItem(
            'Cached Surahs',
            `${offlineStatus.cachedSurahs.length} of 114 surahs cached`
          )}

          {renderSettingItem(
            'Cache Size',
            formatCacheSize(offlineStatus.cacheSize)
          )}

          {offlineStatus.lastUpdated && renderSettingItem(
            'Last Updated',
            offlineStatus.lastUpdated.toLocaleDateString()
          )}

          {renderSettingItem(
            'Cache Popular Surahs',
            'Download commonly read surahs',
            <Ionicons
              name="download-outline"
              size={20}
              color={Colors[colorScheme ?? 'light'].tint}
            />,
            handleCachePopularSurahs
          )}

          {renderSettingItem(
            'Sync Cache',
            'Update cached data',
            <Ionicons
              name="sync-outline"
              size={20}
              color={Colors[colorScheme ?? 'light'].tint}
            />,
            handleSyncCache
          )}

          {renderSettingItem(
            'Clear Cache',
            'Remove all cached data',
            <Ionicons
              name="trash-outline"
              size={20}
              color="#FF6B6B"
            />,
            handleClearCache
          )}
        </View>

        {/* About */}
        <View style={styles.section}>
          <ThemedText type="subtitle" style={styles.sectionTitle}>
            About
          </ThemedText>

          {renderSettingItem(
            'Data Source',
            'Al-Quran Cloud API',
            <Ionicons 
              name="open-outline" 
              size={20} 
              color={Colors[colorScheme ?? 'light'].tabIconDefault} 
            />,
            () => Alert.alert('Data Source', 'This app uses the Al-Quran Cloud API (alquran.cloud) to provide Quranic text and audio.')
          )}

          {renderSettingItem(
            'Version',
            '1.0.0'
          )}
        </View>
      </ScrollView>

      {/* Translation Selection Modal */}
      {renderSelectionModal(
        isTranslationModalVisible,
        () => setIsTranslationModalVisible(false),
        'Select Translation',
        editions.filter(e => e.format === 'text' && e.type === 'translation'),
        (edition: Edition) => (
          <TouchableOpacity
            style={[
              styles.modalItem,
              edition.identifier === settings.translationEdition && {
                backgroundColor: Colors[colorScheme ?? 'light'].tint + '20'
              }
            ]}
            onPress={() => handleTranslationSelect(edition)}
          >
            <ThemedText type="defaultSemiBold" style={styles.modalItemTitle}>
              {edition.englishName}
            </ThemedText>
            <ThemedText type="default" style={styles.modalItemSubtitle}>
              {edition.name}
            </ThemedText>
            {edition.identifier === settings.translationEdition && (
              <Ionicons 
                name="checkmark" 
                size={20} 
                color={Colors[colorScheme ?? 'light'].tint} 
              />
            )}
          </TouchableOpacity>
        ),
        (edition: Edition) => edition.identifier
      )}

      {/* Reciter Selection Modal */}
      {renderSelectionModal(
        isReciterModalVisible,
        () => setIsReciterModalVisible(false),
        'Select Reciter',
        audioReciters,
        (reciter: AudioReciter) => (
          <TouchableOpacity
            style={[
              styles.modalItem,
              reciter.identifier === settings.audioReciter && {
                backgroundColor: Colors[colorScheme ?? 'light'].tint + '20'
              }
            ]}
            onPress={() => handleReciterSelect(reciter)}
          >
            <ThemedText type="defaultSemiBold" style={styles.modalItemTitle}>
              {reciter.englishName}
            </ThemedText>
            <ThemedText type="default" style={styles.modalItemSubtitle}>
              {reciter.name}
            </ThemedText>
            {reciter.identifier === settings.audioReciter && (
              <Ionicons 
                name="checkmark" 
                size={20} 
                color={Colors[colorScheme ?? 'light'].tint} 
              />
            )}
          </TouchableOpacity>
        ),
        (reciter: AudioReciter) => reciter.identifier
      )}

      {/* Theme Selection Modal */}
      {renderSelectionModal(
        isThemeModalVisible,
        () => setIsThemeModalVisible(false),
        'Select Theme',
        [
          { id: 'light', name: 'Light', description: 'Light theme' },
          { id: 'dark', name: 'Dark', description: 'Dark theme' },
          { id: 'sepia', name: 'Sepia', description: 'Warm reading theme' },
        ],
        (theme: any) => (
          <TouchableOpacity
            style={[
              styles.modalItem,
              theme.id === settings.theme && {
                backgroundColor: Colors[colorScheme ?? 'light'].tint + '20'
              }
            ]}
            onPress={() => handleThemeSelect(theme.id)}
          >
            <ThemedText type="defaultSemiBold" style={styles.modalItemTitle}>
              {theme.name}
            </ThemedText>
            <ThemedText type="default" style={styles.modalItemSubtitle}>
              {theme.description}
            </ThemedText>
            {theme.id === settings.theme && (
              <Ionicons 
                name="checkmark" 
                size={20} 
                color={Colors[colorScheme ?? 'light'].tint} 
              />
            )}
          </TouchableOpacity>
        ),
        (theme: any) => theme.id
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  navigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  navButton: {
    padding: 8,
    width: 40,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
    marginHorizontal: 20,
  },
  settingItem: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  settingText: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    opacity: 0.6,
  },
  sliderContainer: {
    width: 120,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E0E0E0',
  },
  cancelButton: {
    fontSize: 16,
    color: '#FF6B6B',
  },
  modalTitle: {
    fontSize: 18,
  },
  modalList: {
    paddingVertical: 10,
  },
  modalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E0E0E0',
  },
  modalItemTitle: {
    flex: 1,
    fontSize: 16,
    marginBottom: 2,
  },
  modalItemSubtitle: {
    flex: 1,
    fontSize: 14,
    opacity: 0.6,
  },
});
