import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useQuran } from '@/contexts/QuranContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { SearchMatch, SearchResult } from '@/types/quran';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

export default function SearchScreen() {
  const colorScheme = useColorScheme();
  const { searchVerses, surahs, settings } = useQuran();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedSurah, setSelectedSurah] = useState<number | 'all'>('all');
  const [searchHistory, setSearchHistory] = useState<string[]>([]);

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery.trim().length >= 3) {
        performSearch();
      } else {
        setSearchResults(null);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, selectedSurah]);

  const performSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      setIsSearching(true);
      const results = await searchVerses(
        searchQuery.trim(),
        selectedSurah,
        settings.translationEdition
      );
      setSearchResults(results);
      
      // Add to search history
      if (!searchHistory.includes(searchQuery.trim())) {
        setSearchHistory(prev => [searchQuery.trim(), ...prev.slice(0, 9)]);
      }
    } catch (error) {
      Alert.alert('Search Error', 'Failed to search verses. Please try again.');
      console.error('Search error:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const handleResultPress = (match: SearchMatch) => {
    router.push(`/(tabs)/quran/surah/${match.surah.number}` as any);
  };

  const handleHistoryItemPress = (query: string) => {
    setSearchQuery(query);
  };

  const clearHistory = () => {
    setSearchHistory([]);
  };

  const renderSearchResult = ({ item }: { item: SearchMatch }) => (
    <TouchableOpacity
      style={[
        styles.resultItem,
        { 
          backgroundColor: Colors[colorScheme ?? 'light'].background,
          borderBottomColor: Colors[colorScheme ?? 'light'].tabIconDefault,
        }
      ]}
      onPress={() => handleResultPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.resultHeader}>
        <View style={styles.surahInfo}>
          <ThemedText type="defaultSemiBold" style={styles.surahName}>
            {item.surah.englishName}
          </ThemedText>
          <ThemedText type="default" style={styles.ayahNumber}>
            Verse {item.numberInSurah}
          </ThemedText>
        </View>
        <View style={[
          styles.surahNumber,
          { backgroundColor: Colors[colorScheme ?? 'light'].tint }
        ]}>
          <Text style={styles.surahNumberText}>{item.surah.number}</Text>
        </View>
      </View>
      
      <ThemedText style={styles.resultText} numberOfLines={3}>
        {item.text}
      </ThemedText>
      
      <View style={styles.resultMeta}>
        <ThemedText type="default" style={styles.metaText}>
          {item.surah.englishNameTranslation}
        </ThemedText>
        <Text style={styles.separator}>•</Text>
        <ThemedText 
          type="default" 
          style={[
            styles.metaText,
            { color: item.surah.revelationType === 'Meccan' ? '#8B5A2B' : '#2B5A8B' }
          ]}
        >
          {item.surah.revelationType}
        </ThemedText>
      </View>
    </TouchableOpacity>
  );

  const renderHistoryItem = ({ item }: { item: string }) => (
    <TouchableOpacity
      style={styles.historyItem}
      onPress={() => handleHistoryItemPress(item)}
    >
      <Ionicons 
        name="time-outline" 
        size={16} 
        color={Colors[colorScheme ?? 'light'].tabIconDefault} 
      />
      <ThemedText style={styles.historyText}>{item}</ThemedText>
    </TouchableOpacity>
  );

  const renderEmptyState = () => {
    if (searchQuery.trim().length === 0) {
      return (
        <View style={styles.emptyState}>
          <Ionicons 
            name="search-outline" 
            size={64} 
            color={Colors[colorScheme ?? 'light'].tabIconDefault} 
          />
          <ThemedText type="subtitle" style={styles.emptyTitle}>
            Search the Quran
          </ThemedText>
          <ThemedText type="default" style={styles.emptyDescription}>
            Enter at least 3 characters to search for verses
          </ThemedText>
          
          {/* Search History */}
          {searchHistory.length > 0 && (
            <View style={styles.historySection}>
              <View style={styles.historyHeader}>
                <ThemedText type="defaultSemiBold" style={styles.historyTitle}>
                  Recent Searches
                </ThemedText>
                <TouchableOpacity onPress={clearHistory}>
                  <ThemedText style={styles.clearButton}>Clear</ThemedText>
                </TouchableOpacity>
              </View>
              <FlatList
                data={searchHistory}
                renderItem={renderHistoryItem}
                keyExtractor={(item, index) => `${item}-${index}`}
                showsVerticalScrollIndicator={false}
              />
            </View>
          )}
        </View>
      );
    }

    if (searchQuery.trim().length < 3) {
      return (
        <View style={styles.emptyState}>
          <Ionicons 
            name="text-outline" 
            size={64} 
            color={Colors[colorScheme ?? 'light'].tabIconDefault} 
          />
          <ThemedText type="subtitle" style={styles.emptyTitle}>
            Keep typing...
          </ThemedText>
          <ThemedText type="default" style={styles.emptyDescription}>
            Enter at least 3 characters to search
          </ThemedText>
        </View>
      );
    }

    if (searchResults && searchResults.matches.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Ionicons 
            name="document-outline" 
            size={64} 
            color={Colors[colorScheme ?? 'light'].tabIconDefault} 
          />
          <ThemedText type="subtitle" style={styles.emptyTitle}>
            No results found
          </ThemedText>
          <ThemedText type="default" style={styles.emptyDescription}>
            Try different keywords or search in all surahs
          </ThemedText>
        </View>
      );
    }

    return null;
  };

  return (
    <ThemedView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.navigation}>
          <TouchableOpacity
            style={styles.navButton}
            onPress={() => router.back()}
          >
            <Ionicons 
              name="arrow-back" 
              size={24} 
              color={Colors[colorScheme ?? 'light'].text} 
            />
          </TouchableOpacity>
          <ThemedText type="title" style={styles.title}>
            Search Quran
          </ThemedText>
          <View style={styles.navButton} />
        </View>

        {/* Search Bar */}
        <View style={[
          styles.searchContainer,
          { 
            backgroundColor: Colors[colorScheme ?? 'light'].background,
            borderColor: Colors[colorScheme ?? 'light'].tabIconDefault,
          }
        ]}>
          <Ionicons 
            name="search" 
            size={20} 
            color={Colors[colorScheme ?? 'light'].tabIconDefault} 
            style={styles.searchIcon}
          />
          <TextInput
            style={[
              styles.searchInput,
              { color: Colors[colorScheme ?? 'light'].text }
            ]}
            placeholder="Search verses..."
            placeholderTextColor={Colors[colorScheme ?? 'light'].tabIconDefault}
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoCapitalize="none"
            autoCorrect={false}
            autoFocus
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons 
                name="close-circle" 
                size={20} 
                color={Colors[colorScheme ?? 'light'].tabIconDefault} 
              />
            </TouchableOpacity>
          )}
        </View>

        {/* Surah Filter */}
        <View style={styles.filterContainer}>
          <ThemedText type="defaultSemiBold" style={styles.filterLabel}>
            Search in:
          </ThemedText>
          <TouchableOpacity
            style={[
              styles.filterButton,
              selectedSurah === 'all' && { backgroundColor: Colors[colorScheme ?? 'light'].tint }
            ]}
            onPress={() => setSelectedSurah('all')}
          >
            <Text style={[
              styles.filterButtonText,
              selectedSurah === 'all' && { color: 'white' }
            ]}>
              All Surahs
            </Text>
          </TouchableOpacity>
          {/* Add more filter options as needed */}
        </View>

        {/* Results Count */}
        {searchResults && (
          <ThemedText type="default" style={styles.resultsCount}>
            {searchResults.matches.length} results found
            {searchResults.totalCount > searchResults.matches.length && 
              ` (showing first ${searchResults.matches.length})`
            }
          </ThemedText>
        )}
      </View>

      {/* Content */}
      {isSearching ? (
        <View style={styles.loadingState}>
          <ActivityIndicator 
            size="large" 
            color={Colors[colorScheme ?? 'light'].tint} 
          />
          <ThemedText type="default" style={styles.loadingText}>
            Searching...
          </ThemedText>
        </View>
      ) : (
        <FlatList
          data={searchResults?.matches || []}
          renderItem={renderSearchResult}
          keyExtractor={(item, index) => `${item.number}-${index}`}
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={
            !searchResults || searchResults.matches.length === 0 
              ? styles.emptyContainer 
              : styles.listContainer
          }
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  navigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  navButton: {
    padding: 8,
    width: 40,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 16,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 4,
  },
  filterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  filterLabel: {
    marginRight: 12,
    fontSize: 14,
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
    marginRight: 8,
  },
  filterButtonText: {
    fontSize: 12,
    fontWeight: '600',
  },
  resultsCount: {
    fontSize: 14,
    opacity: 0.6,
    textAlign: 'center',
  },
  loadingState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  emptyContainer: {
    flexGrow: 1,
  },
  listContainer: {
    paddingBottom: 20,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    textAlign: 'center',
    opacity: 0.6,
    marginBottom: 32,
  },
  historySection: {
    width: '100%',
    maxWidth: 300,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  historyTitle: {
    fontSize: 16,
  },
  clearButton: {
    fontSize: 14,
    color: '#FF6B6B',
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginBottom: 4,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
  },
  historyText: {
    marginLeft: 8,
    fontSize: 14,
  },
  resultItem: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  surahInfo: {
    flex: 1,
  },
  surahName: {
    fontSize: 16,
    marginBottom: 2,
  },
  ayahNumber: {
    fontSize: 12,
    opacity: 0.6,
  },
  surahNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  surahNumberText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  resultText: {
    fontSize: 15,
    lineHeight: 22,
    marginBottom: 8,
  },
  resultMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaText: {
    fontSize: 12,
  },
  separator: {
    marginHorizontal: 8,
    opacity: 0.5,
  },
});
