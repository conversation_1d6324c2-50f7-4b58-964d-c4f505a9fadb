import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useQuran } from '@/contexts/QuranContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ayah } from '@/types/quran';
import { Ionicons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    ScrollView,
    Share,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

export default function SurahReadingScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const colorScheme = useColorScheme();
  const {
    currentSurah,
    loadSurah,
    isLoading,
    error,
    settings,
    bookmarks,
    addBookmark,
    removeBookmark,
    updateReadingProgress,
    audioState,
    playAudio,
    pauseAudio,
    stopAudio,
  } = useQuran();
  
  const [selectedAyah, setSelectedAyah] = useState<number | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);
  const surahNumber = parseInt(id || '1');

  useEffect(() => {
    if (surahNumber) {
      loadSurah(surahNumber, settings.showTranslation ? settings.translationEdition : undefined);
    }
  }, [surahNumber, settings.translationEdition, settings.showTranslation]);

  useEffect(() => {
    // Update reading progress when surah is loaded
    if (currentSurah && currentSurah.ayahs && currentSurah.ayahs.length > 0) {
      updateReadingProgress(surahNumber, 1);
    }
  }, [currentSurah]);

  const handleAyahPress = (ayahNumber: number) => {
    setSelectedAyah(selectedAyah === ayahNumber ? null : ayahNumber);
    updateReadingProgress(surahNumber, ayahNumber);
  };

  const handleBookmarkAyah = async (ayah: Ayah) => {
    if (!currentSurah) return;

    const existingBookmark = bookmarks.find(
      b => b.surahNumber === surahNumber && b.ayahNumber === ayah.numberInSurah
    );

    if (existingBookmark) {
      await removeBookmark(existingBookmark.id);
      Alert.alert('Bookmark Removed', 'Ayah removed from bookmarks');
    } else {
      await addBookmark({
        surahNumber,
        ayahNumber: ayah.numberInSurah,
        surahName: currentSurah.englishName,
        ayahText: ayah.text,
      });
      Alert.alert('Bookmark Added', 'Ayah added to bookmarks');
    }
    setSelectedAyah(null);
  };

  const handleShareAyah = async (ayah: Ayah) => {
    if (!currentSurah) return;

    const shareText = `${currentSurah.englishName} (${currentSurah.number}:${ayah.numberInSurah})\n\n${ayah.text}\n\nShared from Islam Daily`;
    
    try {
      await Share.share({
        message: shareText,
        title: `${currentSurah.englishName} - Ayah ${ayah.numberInSurah}`,
      });
    } catch (error) {
      console.error('Error sharing ayah:', error);
    }
    setSelectedAyah(null);
  };

  const isAyahBookmarked = (ayahNumber: number) => {
    return bookmarks.some(b => b.surahNumber === surahNumber && b.ayahNumber === ayahNumber);
  };

  const renderAyah = (ayah: Ayah, index: number) => {
    const isSelected = selectedAyah === ayah.numberInSurah;
    const isBookmarked = isAyahBookmarked(ayah.numberInSurah);
    const isCurrentlyPlaying = audioState.isPlaying &&
                              audioState.currentSurah === surahNumber &&
                              audioState.currentAyah === ayah.numberInSurah;

    return (
      <View key={ayah.number} style={styles.ayahContainer}>
        <TouchableOpacity
          style={[
            styles.ayahContent,
            isSelected && {
              backgroundColor: Colors[colorScheme ?? 'light'].tint + '20',
              borderColor: Colors[colorScheme ?? 'light'].tint,
              borderWidth: 1,
            },
            isCurrentlyPlaying && {
              backgroundColor: '#4CAF50' + '15',
              borderColor: '#4CAF50',
              borderWidth: 1,
            }
          ]}
          onPress={() => handleAyahPress(ayah.numberInSurah)}
          activeOpacity={0.7}
        >
          {/* Ayah Number */}
          <View style={styles.ayahHeader}>
            <View style={styles.ayahHeaderLeft}>
              <View style={[
                styles.ayahNumber,
                { backgroundColor: isCurrentlyPlaying ? '#4CAF50' : Colors[colorScheme ?? 'light'].tint }
              ]}>
                <Text style={styles.ayahNumberText}>{ayah.numberInSurah}</Text>
              </View>
              {isCurrentlyPlaying && (
                <View style={styles.playingIndicator}>
                  <Ionicons name="volume-high" size={16} color="#4CAF50" />
                </View>
              )}
            </View>
            <View style={styles.ayahHeaderRight}>
              {isBookmarked && (
                <Ionicons
                  name="bookmark"
                  size={16}
                  color={Colors[colorScheme ?? 'light'].tint}
                />
              )}
            </View>
          </View>

          {/* Arabic Text */}
          <Text style={[
            styles.arabicText,
            { 
              color: Colors[colorScheme ?? 'light'].text,
              fontSize: settings.fontSize + 4,
              textAlign: settings.textDirection === 'rtl' ? 'right' : 'left',
            }
          ]}>
            {ayah.text}
          </Text>

          {/* Translation (if enabled) */}
          {settings.showTranslation && currentSurah?.edition?.format === 'text' && (
            <ThemedText style={[
              styles.translationText,
              { fontSize: settings.fontSize - 2 }
            ]}>
              {/* Translation would be loaded from a separate API call */}
              Translation will be displayed here
            </ThemedText>
          )}

          {/* Sajda Indicator */}
          {ayah.sajda && (
            <View style={styles.sajdaIndicator}>
              <Ionicons name="moon" size={16} color={Colors[colorScheme ?? 'light'].tint} />
              <ThemedText style={styles.sajdaText}>Sajda</ThemedText>
            </View>
          )}
        </TouchableOpacity>

        {/* Action Buttons */}
        {isSelected && (
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: Colors[colorScheme ?? 'light'].tint }]}
              onPress={() => handleBookmarkAyah(ayah)}
            >
              <Ionicons 
                name={isBookmarked ? "bookmark" : "bookmark-outline"} 
                size={20} 
                color="white" 
              />
              <Text style={styles.actionButtonText}>
                {isBookmarked ? 'Remove' : 'Bookmark'}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: '#4CAF50' }]}
              onPress={() => handleShareAyah(ayah)}
            >
              <Ionicons name="share-outline" size={20} color="white" />
              <Text style={styles.actionButtonText}>Share</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: '#FF9800' }]}
              onPress={async () => {
                try {
                  if (audioState.isPlaying &&
                      audioState.currentSurah === surahNumber &&
                      audioState.currentAyah === ayah.numberInSurah) {
                    await pauseAudio();
                  } else {
                    await playAudio(surahNumber, ayah.numberInSurah);
                  }
                } catch (error) {
                  Alert.alert('Audio Error', 'Failed to play audio');
                }
                setSelectedAyah(null);
              }}
            >
              <Ionicons
                name={
                  audioState.isPlaying &&
                  audioState.currentSurah === surahNumber &&
                  audioState.currentAyah === ayah.numberInSurah
                    ? "pause-outline"
                    : "play-outline"
                }
                size={20}
                color="white"
              />
              <Text style={styles.actionButtonText}>
                {audioState.isPlaying &&
                 audioState.currentSurah === surahNumber &&
                 audioState.currentAyah === ayah.numberInSurah
                  ? 'Pause'
                  : 'Play'}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };

  const renderHeader = () => {
    if (!currentSurah) return null;

    return (
      <View style={styles.header}>
        {/* Navigation */}
        <View style={styles.navigation}>
          <TouchableOpacity
            style={styles.navButton}
            onPress={() => router.back()}
          >
            <Ionicons 
              name="arrow-back" 
              size={24} 
              color={Colors[colorScheme ?? 'light'].text} 
            />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.navButton}
            onPress={() => {
              // TODO: Open settings/options menu
              console.log('Open options');
            }}
          >
            <Ionicons 
              name="ellipsis-horizontal" 
              size={24} 
              color={Colors[colorScheme ?? 'light'].text} 
            />
          </TouchableOpacity>
        </View>

        {/* Surah Info */}
        <View style={styles.surahInfo}>
          <Text style={[
            styles.arabicSurahName,
            { color: Colors[colorScheme ?? 'light'].text }
          ]}>
            {currentSurah.name}
          </Text>
          
          <ThemedText type="title" style={styles.englishSurahName}>
            {currentSurah.englishName}
          </ThemedText>
          
          <ThemedText type="default" style={styles.surahTranslation}>
            {currentSurah.englishNameTranslation}
          </ThemedText>
          
          <View style={styles.surahMeta}>
            <ThemedText type="defaultSemiBold" style={styles.metaText}>
              {currentSurah.numberOfAyahs} verses
            </ThemedText>
            <Text style={styles.separator}>•</Text>
            <ThemedText 
              type="default" 
              style={[
                styles.metaText,
                { color: currentSurah.revelationType === 'Meccan' ? '#8B5A2B' : '#2B5A8B' }
              ]}
            >
              {currentSurah.revelationType}
            </ThemedText>
          </View>
        </View>

        {/* Bismillah (except for Surah At-Tawbah) */}
        {surahNumber !== 9 && surahNumber !== 1 && (
          <View style={styles.bismillahContainer}>
            <Text style={[
              styles.bismillahText,
              { 
                color: Colors[colorScheme ?? 'light'].text,
                fontSize: settings.fontSize + 2,
              }
            ]}>
              بِسْمِ ٱللَّهِ ٱلرَّحْمَٰنِ ٱلرَّحِيمِ
            </Text>
          </View>
        )}
      </View>
    );
  };

  if (isLoading) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.loadingState}>
          <ActivityIndicator 
            size="large" 
            color={Colors[colorScheme ?? 'light'].tint} 
          />
          <ThemedText type="default" style={styles.loadingText}>
            Loading surah...
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  if (error || !currentSurah) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.errorState}>
          <Ionicons 
            name="alert-circle-outline" 
            size={64} 
            color="#FF6B6B" 
          />
          <ThemedText type="subtitle" style={styles.errorTitle}>
            Failed to load surah
          </ThemedText>
          <ThemedText type="default" style={styles.errorDescription}>
            {error || 'Surah not found'}
          </ThemedText>
          <TouchableOpacity 
            style={[
              styles.retryButton,
              { backgroundColor: Colors[colorScheme ?? 'light'].tint }
            ]}
            onPress={() => loadSurah(surahNumber)}
          >
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {renderHeader()}
        
        {/* Ayahs */}
        <View style={styles.ayahsList}>
          {currentSurah.ayahs?.map((ayah, index) => renderAyah(ayah, index))}
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  loadingState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
    color: '#FF6B6B',
  },
  errorDescription: {
    textAlign: 'center',
    opacity: 0.6,
    marginBottom: 24,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  navigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  navButton: {
    padding: 8,
  },
  surahInfo: {
    alignItems: 'center',
    marginBottom: 20,
  },
  arabicSurahName: {
    fontSize: 32,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  englishSurahName: {
    fontSize: 24,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 4,
  },
  surahTranslation: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: 12,
  },
  surahMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaText: {
    fontSize: 14,
  },
  separator: {
    marginHorizontal: 8,
    opacity: 0.5,
  },
  bismillahContainer: {
    alignItems: 'center',
    paddingVertical: 20,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderColor: '#E0E0E0',
  },
  bismillahText: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
  },
  ayahsList: {
    paddingHorizontal: 20,
  },
  ayahContainer: {
    marginBottom: 16,
  },
  ayahContent: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: 'transparent',
  },
  ayahHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  ayahHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ayahHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  playingIndicator: {
    marginLeft: 8,
  },
  ayahNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  ayahNumberText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  arabicText: {
    fontSize: 22,
    lineHeight: 40,
    textAlign: 'right',
    marginBottom: 12,
    fontWeight: '500',
  },
  translationText: {
    fontSize: 16,
    lineHeight: 24,
    opacity: 0.8,
    marginBottom: 8,
  },
  sajdaIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  sajdaText: {
    marginLeft: 6,
    fontSize: 12,
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 12,
    paddingHorizontal: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 80,
    justifyContent: 'center',
  },
  actionButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
});
