import { quran<PERSON>pi } from '@/services/quranApi';
import {
    AudioReciter,
    AudioState,
    Bookmark,
    Edition,
    QuranContextType,
    QuranSettings,
    ReadingProgress,
    SearchResult,
    Surah,
} from '@/types/quran';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Audio } from 'expo-av';
import React, { createContext, ReactNode, useContext, useEffect, useReducer } from 'react';

// Storage Keys
const SETTINGS_KEY = 'quran_settings';
const BOOKMARKS_KEY = 'quran_bookmarks';
const PROGRESS_KEY = 'quran_progress';

// Default Settings
const DEFAULT_SETTINGS: QuranSettings = {
  fontSize: 18,
  fontFamily: 'System',
  textDirection: 'rtl',
  showTranslation: true,
  translationEdition: 'en.asad',
  showTransliteration: false,
  transliterationEdition: 'en.transliteration',
  audioReciter: 'ar.alafasy',
  autoPlay: false,
  repeatMode: 'none',
  theme: 'light',
};

// State Type
interface QuranState {
  surahs: Surah[];
  currentSurah?: Surah;
  editions: Edition[];
  audioReciters: AudioReciter[];
  isLoading: boolean;
  error?: string;
  settings: QuranSettings;
  bookmarks: Bookmark[];
  readingProgress: ReadingProgress[];
  audioState: AudioState;
  sound?: Audio.Sound;
}

// Action Types
type QuranAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | undefined }
  | { type: 'SET_SURAHS'; payload: Surah[] }
  | { type: 'SET_CURRENT_SURAH'; payload: Surah }
  | { type: 'SET_EDITIONS'; payload: Edition[] }
  | { type: 'SET_AUDIO_RECITERS'; payload: AudioReciter[] }
  | { type: 'SET_SETTINGS'; payload: QuranSettings }
  | { type: 'SET_BOOKMARKS'; payload: Bookmark[] }
  | { type: 'ADD_BOOKMARK'; payload: Bookmark }
  | { type: 'REMOVE_BOOKMARK'; payload: string }
  | { type: 'UPDATE_BOOKMARK'; payload: { id: string; updates: Partial<Bookmark> } }
  | { type: 'SET_READING_PROGRESS'; payload: ReadingProgress[] }
  | { type: 'UPDATE_READING_PROGRESS'; payload: ReadingProgress }
  | { type: 'SET_AUDIO_STATE'; payload: Partial<AudioState> }
  | { type: 'SET_SOUND'; payload: Audio.Sound | undefined };

// Initial State
const initialState: QuranState = {
  surahs: [],
  editions: [],
  audioReciters: [],
  isLoading: false,
  settings: DEFAULT_SETTINGS,
  bookmarks: [],
  readingProgress: [],
  audioState: {
    isPlaying: false,
    isLoading: false,
    duration: 0,
    position: 0,
    reciter: DEFAULT_SETTINGS.audioReciter,
    playbackRate: 1.0,
  },
};

// Reducer
function quranReducer(state: QuranState, action: QuranAction): QuranState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'SET_SURAHS':
      return { ...state, surahs: action.payload };
    
    case 'SET_CURRENT_SURAH':
      return { ...state, currentSurah: action.payload };
    
    case 'SET_EDITIONS':
      return { ...state, editions: action.payload };
    
    case 'SET_AUDIO_RECITERS':
      return { ...state, audioReciters: action.payload };
    
    case 'SET_SETTINGS':
      return { ...state, settings: action.payload };
    
    case 'SET_BOOKMARKS':
      return { ...state, bookmarks: action.payload };
    
    case 'ADD_BOOKMARK':
      return { ...state, bookmarks: [...state.bookmarks, action.payload] };
    
    case 'REMOVE_BOOKMARK':
      return {
        ...state,
        bookmarks: state.bookmarks.filter(b => b.id !== action.payload),
      };
    
    case 'UPDATE_BOOKMARK':
      return {
        ...state,
        bookmarks: state.bookmarks.map(b =>
          b.id === action.payload.id ? { ...b, ...action.payload.updates } : b
        ),
      };
    
    case 'SET_READING_PROGRESS':
      return { ...state, readingProgress: action.payload };
    
    case 'UPDATE_READING_PROGRESS':
      const existingIndex = state.readingProgress.findIndex(
        p => p.surahNumber === action.payload.surahNumber
      );
      
      if (existingIndex >= 0) {
        const updated = [...state.readingProgress];
        updated[existingIndex] = action.payload;
        return { ...state, readingProgress: updated };
      } else {
        return {
          ...state,
          readingProgress: [...state.readingProgress, action.payload],
        };
      }
    
    case 'SET_AUDIO_STATE':
      return {
        ...state,
        audioState: { ...state.audioState, ...action.payload },
      };
    
    case 'SET_SOUND':
      return { ...state, sound: action.payload };
    
    default:
      return state;
  }
}

// Context
const QuranContext = createContext<QuranContextType | undefined>(undefined);

// Provider Component
interface QuranProviderProps {
  children: ReactNode;
}

export function QuranProvider({ children }: QuranProviderProps) {
  const [state, dispatch] = useReducer(quranReducer, initialState);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Audio setup
  useEffect(() => {
    setupAudio();
    return () => {
      if (state.sound) {
        state.sound.unloadAsync();
      }
    };
  }, []);

  const setupAudio = async () => {
    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });
    } catch (error) {
      console.warn('Failed to setup audio:', error);
    }
  };

  const loadInitialData = async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      // Load stored data
      await Promise.all([
        loadSettings(),
        loadBookmarks(),
        loadReadingProgress(),
      ]);

      // Load API data
      await Promise.all([
        loadSurahs(),
        loadEditions(),
        loadAudioReciters(),
      ]);

      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Unknown error' });
    }
  };

  // Storage functions
  const loadSettings = async () => {
    try {
      const stored = await AsyncStorage.getItem(SETTINGS_KEY);
      if (stored) {
        const settings = { ...DEFAULT_SETTINGS, ...JSON.parse(stored) };
        dispatch({ type: 'SET_SETTINGS', payload: settings });
      }
    } catch (error) {
      console.warn('Failed to load settings:', error);
    }
  };

  const saveSettings = async (settings: QuranSettings) => {
    try {
      await AsyncStorage.setItem(SETTINGS_KEY, JSON.stringify(settings));
    } catch (error) {
      console.warn('Failed to save settings:', error);
    }
  };

  const loadBookmarks = async () => {
    try {
      const stored = await AsyncStorage.getItem(BOOKMARKS_KEY);
      if (stored) {
        const bookmarks = JSON.parse(stored);
        dispatch({ type: 'SET_BOOKMARKS', payload: bookmarks });
      }
    } catch (error) {
      console.warn('Failed to load bookmarks:', error);
    }
  };

  const saveBookmarks = async (bookmarks: Bookmark[]) => {
    try {
      await AsyncStorage.setItem(BOOKMARKS_KEY, JSON.stringify(bookmarks));
    } catch (error) {
      console.warn('Failed to save bookmarks:', error);
    }
  };

  const loadReadingProgress = async () => {
    try {
      const stored = await AsyncStorage.getItem(PROGRESS_KEY);
      if (stored) {
        const progress = JSON.parse(stored);
        dispatch({ type: 'SET_READING_PROGRESS', payload: progress });
      }
    } catch (error) {
      console.warn('Failed to load reading progress:', error);
    }
  };

  const saveReadingProgress = async (progress: ReadingProgress[]) => {
    try {
      await AsyncStorage.setItem(PROGRESS_KEY, JSON.stringify(progress));
    } catch (error) {
      console.warn('Failed to save reading progress:', error);
    }
  };

  // API functions
  const loadSurahs = async () => {
    try {
      const surahs = await quranApi.getSurahs();
      dispatch({ type: 'SET_SURAHS', payload: surahs });
    } catch (error) {
      throw new Error(`Failed to load surahs: ${error}`);
    }
  };

  const loadSurah = async (surahNumber: number, edition?: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const surah = await quranApi.getSurah(surahNumber, edition);
      dispatch({ type: 'SET_CURRENT_SURAH', payload: surah });
      dispatch({ type: 'SET_LOADING', payload: false });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Unknown error' });
    }
  };

  const loadEditions = async () => {
    try {
      const editions = await quranApi.getEditions();
      dispatch({ type: 'SET_EDITIONS', payload: editions });
    } catch (error) {
      console.warn('Failed to load editions:', error);
    }
  };

  const loadAudioReciters = async () => {
    try {
      const reciters = await quranApi.getEditions('audio', 'ar') as AudioReciter[];
      dispatch({ type: 'SET_AUDIO_RECITERS', payload: reciters });
    } catch (error) {
      console.warn('Failed to load audio reciters:', error);
    }
  };

  const searchVerses = async (query: string, surah?: number, edition?: string): Promise<SearchResult> => {
    try {
      return await quranApi.searchVerses(query, surah || 'all', edition);
    } catch (error) {
      throw new Error(`Search failed: ${error}`);
    }
  };

  // Context value
  const contextValue: QuranContextType = {
    // Data
    surahs: state.surahs,
    currentSurah: state.currentSurah,
    editions: state.editions,
    audioReciters: state.audioReciters,
    
    // State
    isLoading: state.isLoading,
    error: state.error,
    settings: state.settings,
    bookmarks: state.bookmarks,
    readingProgress: state.readingProgress,
    audioState: state.audioState,
    
    // Actions
    loadSurahs,
    loadSurah,
    searchVerses,
    
    // Settings
    updateSettings: async (updates: Partial<QuranSettings>) => {
      const newSettings = { ...state.settings, ...updates };
      dispatch({ type: 'SET_SETTINGS', payload: newSettings });
      await saveSettings(newSettings);
    },
    
    // Bookmarks
    addBookmark: async (bookmark: Omit<Bookmark, 'id' | 'createdAt'>) => {
      const newBookmark: Bookmark = {
        ...bookmark,
        id: Date.now().toString(),
        createdAt: new Date(),
      };
      dispatch({ type: 'ADD_BOOKMARK', payload: newBookmark });
      await saveBookmarks([...state.bookmarks, newBookmark]);
    },
    
    removeBookmark: async (bookmarkId: string) => {
      dispatch({ type: 'REMOVE_BOOKMARK', payload: bookmarkId });
      const updated = state.bookmarks.filter(b => b.id !== bookmarkId);
      await saveBookmarks(updated);
    },
    
    updateBookmark: async (bookmarkId: string, updates: Partial<Bookmark>) => {
      dispatch({ type: 'UPDATE_BOOKMARK', payload: { id: bookmarkId, updates } });
      const updated = state.bookmarks.map(b =>
        b.id === bookmarkId ? { ...b, ...updates } : b
      );
      await saveBookmarks(updated);
    },
    
    // Reading Progress
    updateReadingProgress: async (surahNumber: number, ayahNumber: number) => {
      const progress: ReadingProgress = {
        surahNumber,
        ayahNumber,
        timestamp: new Date(),
        percentage: (ayahNumber / (state.surahs.find(s => s.number === surahNumber)?.numberOfAyahs || 1)) * 100,
      };
      dispatch({ type: 'UPDATE_READING_PROGRESS', payload: progress });
      
      const updated = state.readingProgress.filter(p => p.surahNumber !== surahNumber);
      updated.push(progress);
      await saveReadingProgress(updated);
    },
    
    // Audio
    playAudio: async (surahNumber: number, ayahNumber?: number) => {
      try {
        dispatch({ type: 'SET_AUDIO_STATE', payload: { isLoading: true, error: undefined } });

        // Stop current audio if playing
        if (state.sound) {
          await state.sound.unloadAsync();
          dispatch({ type: 'SET_SOUND', payload: undefined });
        }

        const startAyah = ayahNumber || 1;
        const audioUrl = quranApi.getAudioUrl(surahNumber, startAyah, state.audioState.reciter);

        const { sound } = await Audio.Sound.createAsync(
          { uri: audioUrl },
          {
            shouldPlay: true,
            rate: state.audioState.playbackRate,
            volume: 1.0,
          }
        );

        dispatch({ type: 'SET_SOUND', payload: sound });
        dispatch({
          type: 'SET_AUDIO_STATE',
          payload: {
            isPlaying: true,
            isLoading: false,
            currentSurah: surahNumber,
            currentAyah: startAyah,
            error: undefined,
          }
        });

        // Set up audio status updates
        sound.setOnPlaybackStatusUpdate((status) => {
          if (status.isLoaded) {
            dispatch({
              type: 'SET_AUDIO_STATE',
              payload: {
                isPlaying: status.isPlaying || false,
                duration: status.durationMillis || 0,
                position: status.positionMillis || 0,
              },
            });

            // Auto-play next ayah if current one finishes
            if (status.didJustFinish && state.settings.autoPlay) {
              const currentSurah = state.surahs.find(s => s.number === surahNumber);
              if (currentSurah && startAyah < currentSurah.numberOfAyahs) {
                contextValue.playAudio(surahNumber, startAyah + 1);
              }
            }
          }
        });

      } catch (error) {
        dispatch({
          type: 'SET_AUDIO_STATE',
          payload: {
            isLoading: false,
            isPlaying: false,
            error: error instanceof Error ? error.message : 'Audio playback failed',
          }
        });
      }
    },

    pauseAudio: async () => {
      try {
        if (state.sound) {
          await state.sound.pauseAsync();
          dispatch({ type: 'SET_AUDIO_STATE', payload: { isPlaying: false } });
        }
      } catch (error) {
        console.error('Failed to pause audio:', error);
      }
    },

    stopAudio: async () => {
      try {
        if (state.sound) {
          await state.sound.stopAsync();
          await state.sound.unloadAsync();
          dispatch({ type: 'SET_SOUND', payload: undefined });
          dispatch({
            type: 'SET_AUDIO_STATE',
            payload: {
              isPlaying: false,
              currentSurah: undefined,
              currentAyah: undefined,
              position: 0,
            }
          });
        }
      } catch (error) {
        console.error('Failed to stop audio:', error);
      }
    },

    seekAudio: async (position: number) => {
      try {
        if (state.sound) {
          await state.sound.setPositionAsync(position);
          dispatch({ type: 'SET_AUDIO_STATE', payload: { position } });
        }
      } catch (error) {
        console.error('Failed to seek audio:', error);
      }
    },
    
    setPlaybackRate: async (rate: number) => {
      dispatch({ type: 'SET_AUDIO_STATE', payload: { playbackRate: rate } });
    },
    
    changeReciter: async (reciterId: string) => {
      dispatch({ type: 'SET_AUDIO_STATE', payload: { reciter: reciterId } });
      await contextValue.updateSettings({ audioReciter: reciterId });
    },
  };

  return (
    <QuranContext.Provider value={contextValue}>
      {children}
    </QuranContext.Provider>
  );
}

// Hook
export function useQuran(): QuranContextType {
  const context = useContext(QuranContext);
  if (!context) {
    throw new Error('useQuran must be used within a QuranProvider');
  }
  return context;
}
