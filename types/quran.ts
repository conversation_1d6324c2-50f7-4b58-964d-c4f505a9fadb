// Quran API Response Types
export interface ApiResponse<T> {
  code: number;
  status: string;
  data: T;
}

// Basic Quran Data Types
export interface Surah {
  number: number;
  name: string;
  englishName: string;
  englishNameTranslation: string;
  numberOfAyahs: number;
  revelationType: 'Meccan' | 'Medinan';
  ayahs?: Ayah[];
  edition?: Edition;
}

export interface Ayah {
  number: number;
  text: string;
  numberInSurah: number;
  juz: number;
  manzil: number;
  page: number;
  ruku: number;
  hizbQuarter: number;
  sajda: boolean | SajdaInfo;
  audio?: string;
  audioSecondary?: string[];
}

export interface SajdaInfo {
  id: number;
  recommended: boolean;
  obligatory: boolean;
}

export interface Edition {
  identifier: string;
  language: string;
  name: string;
  englishName: string;
  format: 'text' | 'audio';
  type: 'quran' | 'translation' | 'transliteration' | 'tafsir' | 'versebyverse';
  direction?: 'rtl' | 'ltr';
}

// Search Types
export interface SearchResult {
  count: number;
  totalCount: number;
  totalPages: number;
  currentPage: number;
  matches: SearchMatch[];
}

export interface SearchMatch {
  number: number;
  text: string;
  edition: Edition;
  surah: {
    number: number;
    name: string;
    englishName: string;
    englishNameTranslation: string;
    revelationType: string;
    numberOfAyahs: number;
  };
  numberInSurah: number;
}

// Audio Types
export interface AudioReciter {
  identifier: string;
  name: string;
  englishName: string;
  language: string;
  format: 'audio';
  type: 'versebyverse' | 'translation';
}

// User Preferences and State Types
export interface QuranSettings {
  fontSize: number;
  fontFamily: string;
  textDirection: 'rtl' | 'ltr';
  showTranslation: boolean;
  translationEdition: string;
  showTransliteration: boolean;
  transliterationEdition: string;
  audioReciter: string;
  autoPlay: boolean;
  repeatMode: 'none' | 'verse' | 'surah';
  theme: 'light' | 'dark' | 'sepia';
}

export interface Bookmark {
  id: string;
  surahNumber: number;
  ayahNumber: number;
  surahName: string;
  ayahText: string;
  note?: string;
  createdAt: Date;
  tags?: string[];
}

export interface ReadingProgress {
  surahNumber: number;
  ayahNumber: number;
  timestamp: Date;
  percentage: number;
}

// Audio Playback State
export interface AudioState {
  isPlaying: boolean;
  isLoading: boolean;
  currentSurah?: number;
  currentAyah?: number;
  duration: number;
  position: number;
  reciter: string;
  playbackRate: number;
  error?: string;
}

// Navigation and UI Types
export interface SurahListItem {
  number: number;
  name: string;
  englishName: string;
  englishNameTranslation: string;
  numberOfAyahs: number;
  revelationType: 'Meccan' | 'Medinan';
  isBookmarked?: boolean;
  lastRead?: Date;
}

export interface QuranScreenParams {
  SurahList: undefined;
  SurahReading: {
    surahNumber: number;
    ayahNumber?: number;
  };
  Search: {
    query?: string;
  };
  Bookmarks: undefined;
  Settings: undefined;
  AudioPlayer: {
    surahNumber: number;
    ayahNumber?: number;
  };
}

// Context Types
export interface QuranContextType {
  // Data
  surahs: Surah[];
  currentSurah?: Surah;
  editions: Edition[];
  audioReciters: AudioReciter[];
  
  // State
  isLoading: boolean;
  error?: string;
  settings: QuranSettings;
  bookmarks: Bookmark[];
  readingProgress: ReadingProgress[];
  audioState: AudioState;
  offlineStatus: {
    cachedSurahs: number[];
    cachedEditions: string[];
    cacheSize: number;
    lastUpdated: Date | null;
  };
  
  // Actions
  loadSurahs: () => Promise<void>;
  loadSurah: (surahNumber: number, edition?: string) => Promise<void>;
  searchVerses: (query: string, surah?: number, edition?: string) => Promise<SearchResult>;
  
  // Settings
  updateSettings: (settings: Partial<QuranSettings>) => Promise<void>;
  
  // Bookmarks
  addBookmark: (bookmark: Omit<Bookmark, 'id' | 'createdAt'>) => Promise<void>;
  removeBookmark: (bookmarkId: string) => Promise<void>;
  updateBookmark: (bookmarkId: string, updates: Partial<Bookmark>) => Promise<void>;
  
  // Reading Progress
  updateReadingProgress: (surahNumber: number, ayahNumber: number) => Promise<void>;
  
  // Audio
  playAudio: (surahNumber: number, ayahNumber?: number) => Promise<void>;
  pauseAudio: () => Promise<void>;
  stopAudio: () => Promise<void>;
  seekAudio: (position: number) => Promise<void>;
  setPlaybackRate: (rate: number) => Promise<void>;
  changeReciter: (reciterId: string) => Promise<void>;

  // Offline functionality
  cachePopularSurahs: () => Promise<void>;
  cacheAudioFiles: (surahNumbers: number[], reciterId?: string) => Promise<void>;
  syncCache: () => Promise<void>;
  clearCache: () => Promise<void>;
  refreshOfflineStatus: () => Promise<void>;
}

// API Service Types
export interface QuranApiService {
  getSurahs(): Promise<Surah[]>;
  getSurah(surahNumber: number, edition?: string): Promise<Surah>;
  getAyah(ayahNumber: number, edition?: string): Promise<Ayah>;
  getEditions(format?: 'text' | 'audio', language?: string, type?: string): Promise<Edition[]>;
  searchVerses(query: string, surah?: number | 'all', edition?: string): Promise<SearchResult>;
  getAudioUrl(surahNumber: number, ayahNumber: number, reciterId: string): string;
}

// Cache Types
export interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

export interface QuranCache {
  surahs?: CacheItem<Surah[]>;
  surahData?: Record<string, CacheItem<Surah>>;
  editions?: CacheItem<Edition[]>;
  searchResults?: Record<string, CacheItem<SearchResult>>;
}
